(function () {
    'use strict';

    angular.module('bbcWeb').controller('ClientSecretAdmCrudController', ClientSecretAdmCrudController);

    ClientSecretAdmCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function ClientSecretAdmCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;

        vm.isNew = function () {
            return $stateParams.link === 'novo';
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.isSaving = false;

        vm.voltar = function () {
            $state.go('client-secret-adm.index');
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving) return;

            vm.isSaving = true;

            var saveClientSecretAdm = {
                login: vm.clientSecretAdm.login,
                senha: vm.clientSecretAdm.senha,
                clientSecret: vm.clientSecretAdm.clientSecret,
                descricao: vm.clientSecretAdm.descricao,
                id: vm.clientSecretAdm.id === "Auto" ? 0 : vm.clientSecretAdm.id
            }

            if (!vm.isNew() && !vm.clientSecretAdm.senha) {
                delete saveClientSecretAdm.senha; // Não alterar senha se não informada
            }

            abrirModal(saveClientSecretAdm);
        };

        function abrirModal(saveClientSecretAdm) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/client-secret-adm/modal/client-secret-adm-modal.html',
                controller: function ($uibModalInstance, toastr, BaseService, saveClientSecretAdm, $rootScope) {
                    var vm = this;
                    vm.carregandoEdit = true;

                    BaseService.post('ClientSecretAdm', 'SaveClientSecretAdm', saveClientSecretAdm).then(function (response) {
                        vm.carregandoEdit = false;
                        if (response.success) {
                            toastr.success(response.message);
                            vm.clientSecretAdm = response.data
                        } else {
                            $uibModalInstance.close();
                            toastr.error(response.message);
                        }

                    });

                    vm.closeModal = function () {
                        $uibModalInstance.close();
                        $state.go('client-secret-adm.index');
                    };

                    vm.copiar = function () {
                        if (navigator.clipboard) {
                            navigator.clipboard.writeText(vm.clientSecretAdm).then(function () {
                                toastr.success('Client Secret copiado para a área de transferência!');
                            });
                        } else {
                            // Fallback para navegadores mais antigos
                            var textArea = document.createElement("textarea");
                            textArea.value = vm.clientSecretAdm;
                            document.body.appendChild(textArea);
                            textArea.focus();
                            textArea.select();
                            try {
                                document.execCommand('copy');
                                toastr.success('Client Secret copiado para a área de transferência!');
                            } catch (err) {
                                toastr.error('Erro ao copiar Client Secret');
                            }
                            document.body.removeChild(textArea);
                        }
                    };
                },
                controllerAs: 'vm',
                resolve: {
                    saveClientSecretAdm: function () {
                        return saveClientSecretAdm;
                    }
                }
            }).result.then(function () {
                vm.isSaving = false;
            }, function () {
                vm.isSaving = false;
            });
        }

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Client Secret Adm',
            link: 'client-secret-adm.index'
        }, {
            name: $stateParams.link === 'novo' ? 'Novo' : 'Editar'
        }];

        vm.clientSecretAdm = {
            ativo: 1
        };

        // Carregar dados se for edição
        if (!vm.isNew()) {
            BaseService.get('ClientSecretAdm', 'ConsultarPorId', { idAuthClientSecret: $stateParams.link }).then(function (response) {
                if (response.success) {
                    vm.clientSecretAdm = response.data;
                    vm.clientSecretAdm.senha = ''; // Não carregar senha por segurança
                } else {
                    toastr.error(response.message);
                    vm.voltar();
                }
            });
        }
    }
})();
