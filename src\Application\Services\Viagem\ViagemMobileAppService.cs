using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic;
using System.Linq.Expressions;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.Interface.DeclaracaoCiot;
using SistemaInfo.BBC.Application.Interface.GrupoEmpresa;
using SistemaInfo.BBC.Application.Interface.Mensagem;
using SistemaInfo.BBC.Application.Interface.PagamentoEvento;
using SistemaInfo.BBC.Application.Interface.Parametros;
using SistemaInfo.BBC.Application.Interface.Pix;
using SistemaInfo.BBC.Application.Interface.Transacao;
using SistemaInfo.BBC.Application.Interface.Veiculo;
using SistemaInfo.BBC.Application.Interface.Viagem;
using SistemaInfo.BBC.Application.Objects.Api.Pix;
using SistemaInfo.BBC.Application.Objects.Api.Pix.Transferencia;
using SistemaInfo.BBC.Application.Objects.Api.Transacao;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Request;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Response;
using SistemaInfo.BBC.Application.Objects.Web.Mensagem;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.BBC.Application.Objects.Web.Transacao;
using SistemaInfo.BBC.Application.Objects.Web.Viagem;
using SistemaInfo.BBC.Domain.CloudTranslationService;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Cartao;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Transferencia;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Cidade.Repository;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.Notificacao;
using SistemaInfo.BBC.Domain.Models.Notificacao.Commands;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Commands;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Repository;
using SistemaInfo.BBC.Domain.Models.PagamentoEventoHistorico.Repository;
using SistemaInfo.BBC.Domain.Models.Pagamentos.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;
using SistemaInfo.BBC.Domain.Models.PercentualTransferencia.Repository;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaPortador.Repository;
using SistemaInfo.BBC.Domain.Models.Pix.Responses.Transferencia;
using SistemaInfo.BBC.Domain.Models.Portador.Commands;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.BBC.Domain.Models.Transacao.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.BBC.Domain.Models.Viagem.Commands;
using SistemaInfo.BBC.Domain.Models.Viagem.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;


namespace SistemaInfo.BBC.Application.Services.Viagem
{
    public class ViagemMobileAppService :
        AppService<Domain.Models.Viagem.Viagem, IViagemReadRepository, IViagemWriteRepository>,
        IViagemMobileAppService
    {
        private readonly ICartaoRepository _cartaoRepository;
        private readonly IEmpresaReadRepository _empresaReadRepository;
        private readonly IPortadorReadRepository _portadorReadRepository;
        private readonly ICidadeReadRepository _cidadeReadRepository;
        private readonly IPagamentoEventoReadRepository _pagamentoEventoReadRepository;
        private readonly IPagamentoEventoWriteRepository _pagamentoEventoWriteRepository;
        private readonly IPagamentoEventoHistoricoReadRepository _pagamentoEventoHistoricoReadRepository;
        private readonly ITransferenciaRepository _transferenciaRepository;
        private readonly IParametrosAppService _parametrosAppService;
        private readonly IUsuarioRepository _usuarioExternalRepository;
        private readonly IUsuarioReadRepository _usuarioReadRepository;
        private readonly IPercentualTransferenciaReadRepository _percentualTransferenciaReadRepository;
        private readonly IPercentualTransferenciaPortadorReadRepository _percentualTransferenciaPortadorReadRepository;
        private readonly IDeclaracaoCiotAppService _declaracaoCiotAppService;
        private readonly IVeiculoAppService _veiculoAppService;
        private readonly IPixAppService _pixAppService;
        private readonly ITransacaoRegisterAppService _transacaoRegisterAppService;
        private readonly IMensagemAppService _mensagemAppService;
        private readonly ITransacaoReadRepository _transacaoReadRepository;
        private readonly IParametrosReadRepository _parametrosReadRepository;
        private readonly IGrupoEmpresaAppService _grupoEmpresaAppService;
        private readonly ICloudTranslationService _cloudTranslationService;
        private IPagamentoEventoAppSerivce _pagamentoEventoAppSerivce;
        private readonly IPagamentosReadRepository _pagamentosReadRepository;
        private readonly IPagamentosWriteRepository _pagamentosWriteRepository;

        public ViagemMobileAppService(IAppEngine engine,
            IViagemReadRepository readRepository,
            IViagemWriteRepository writeRepository,
            IEmpresaReadRepository empresaReadRepository,
            ICartaoRepository cartaoRepository,
            IPortadorReadRepository portadorReadRepository,
            ICidadeReadRepository cidadeReadRepository,
            IPagamentoEventoReadRepository pagamentoEventoReadRepository,
            IPagamentoEventoHistoricoReadRepository pagamentoEventoHistoricoReadRepository,
            ITransferenciaRepository transferenciaRepository,
            IParametrosAppService parametrosAppService,
            IUsuarioRepository usuarioExternalRepository,
            IPercentualTransferenciaReadRepository percentualTransferenciaReadRepository,
            IPercentualTransferenciaPortadorReadRepository percentualTransferenciaPortadorReadRepository,
            IDeclaracaoCiotAppService declaracaoCiotAppService,
            IVeiculoAppService veiculoAppService,
            IPixAppService pixAppService,
            IPagamentoEventoWriteRepository pagamentoEventoWriteRepository,
            IParametrosReadRepository parametrosReadRepository,
            ITransacaoRegisterAppService transacaoRegisterAppService,
            IGrupoEmpresaAppService grupoEmpresaAppService,
            ITransacaoReadRepository transacaoReadRepository, IUsuarioReadRepository usuarioReadRepository,
            ICloudTranslationService cloudTranslationService, IPagamentoEventoAppSerivce pagamentoEventoAppSerivce,
            IMensagemAppService mensagemAppService,
            IPagamentosReadRepository pagamentosReadRepository,
            IPagamentosWriteRepository pagamentosWriteRepository)
            : base(engine, readRepository, writeRepository)
        {
            _empresaReadRepository = empresaReadRepository;
            _cartaoRepository = cartaoRepository;
            _portadorReadRepository = portadorReadRepository;
            _cidadeReadRepository = cidadeReadRepository;
            _pagamentoEventoReadRepository = pagamentoEventoReadRepository;
            _pagamentoEventoHistoricoReadRepository = pagamentoEventoHistoricoReadRepository;
            _transferenciaRepository = transferenciaRepository;
            _parametrosAppService = parametrosAppService;
            _usuarioExternalRepository = usuarioExternalRepository;
            _percentualTransferenciaReadRepository = percentualTransferenciaReadRepository;
            _percentualTransferenciaPortadorReadRepository = percentualTransferenciaPortadorReadRepository;
            _declaracaoCiotAppService = declaracaoCiotAppService;
            _veiculoAppService = veiculoAppService;
            _pixAppService = pixAppService;
            _pagamentoEventoWriteRepository = pagamentoEventoWriteRepository;
            _parametrosReadRepository = parametrosReadRepository;
            _transacaoRegisterAppService = transacaoRegisterAppService;
            _grupoEmpresaAppService = grupoEmpresaAppService;
            _transacaoReadRepository = transacaoReadRepository;
            _cloudTranslationService = cloudTranslationService;
            _pagamentoEventoAppSerivce = pagamentoEventoAppSerivce;
            _usuarioReadRepository = usuarioReadRepository;
            _mensagemAppService = mensagemAppService;
            _pagamentosReadRepository = pagamentosReadRepository;
            _pagamentosWriteRepository = pagamentosWriteRepository;
        }
        
        public async Task<ConsultarViagensResponse> ConsultarViagemMobile(ConsultarViagensRequest request)
        {
            #region 1. Validar filtros de entrada
            var validacao = request.validarFiltros();
            if (validacao is not null)
                throw new Exception(validacao);
            #endregion
           
            #region 2. Query base com Includes necessários
            var query = Repository.Query.AsNoTracking()
                .Include(v => v.Empresa)
                .Include(v => v.PortadorProprietario)
                .Include(v => v.PortadorMotorista)
                .Include(v => v.CidadeOrigem)
                .Include(v => v.CidadeDestino)
                .Include(v => v.PagamentoEvento)
                .AsQueryable();
            #endregion
            
            #region 3. Filtros de Viagem
            if (request.ViagemId.HasValue && request.ViagemExternoId.HasValue)
            {
                query = query.Where(v =>
                    v.Id == request.ViagemId && v.ViagemExternoId == request.ViagemExternoId);
            }
            else
            {
                if (request.ViagemId > 0)
                    query = query.Where(v => v.Id == request.ViagemId);
            
                if (request.ViagemExternoId > 0)
                    query = query.Where(v => v.ViagemExternoId == request.ViagemExternoId);
            }
            #endregion
             
            #region 4. Filtro por CPF/CNPJ
            if (!string.IsNullOrWhiteSpace(request.CpfCnpj))
                query = query.Where(v => v.PortadorProprietario.CpfCnpj == request.CpfCnpj || v.PortadorMotorista.CpfCnpj == request.CpfCnpj);
            #endregion#endregion
            
            #region  5. Filtro de datas
            if (request.DataInicial.HasValue)
                query = query.Where(v => v.DataCadastro >= request.DataInicial.Value.Date);

            if (request.DataFinal.HasValue)
                query = query.Where(v => v.DataCadastro < request.DataFinal.Value.Date.AddDays(1));
            #endregion 

            #region  6. Ordenação
            query = request.Ordem.ToUpper() == "ASC"
                ? query.OrderBy(v => v.DataCadastro)
                : query.OrderByDescending(v => v.DataCadastro);
            #endregion 
            
            #region 7. Paginação e Mapeamento
            var total = await query.CountAsync();
            
            var viagens = await query
                .Skip(request.Page * request.Limit)
                .Take(request.Limit)
                .Select(d => Mapper.Map<ViagemResponseItem>(d))
                .ToListAsync();
            #endregion
            
            #region 8. Resposta final
            return new ConsultarViagensResponse
            {
                Page = request.Page,
                Limit = request.Limit,
                TotalPages = (int) Math.Ceiling((double) total / request.Limit),
                TotalItems = total,
                Result = viagens
            };
            #endregion
        }

        public async Task<ConsultarPagamentoResponse> ConsultarPagamentoMobile(ConsultarPagamentosRequest request)
        {
            #region 1. Validar filtros de entrada
            var validacao = request.validarFiltros();
            if (validacao is not null)
                throw new Exception(validacao);
            #endregion
            
            #region 2. Query base com Includes necessários
            var query = _pagamentoEventoReadRepository.AsNoTracking()
                .Include(v => v.Empresa)
                .Include(v => v.Viagem).ThenInclude(v => v.PortadorProprietario)
                .Include(v => v.Viagem).ThenInclude(v => v.PortadorMotorista)
                .Include(v => v.UsuarioCadastro)
                .Include(v => v.Transacao)
                .AsQueryable();
            #endregion
            
            #region 3. Filtros de Viagem
            if (request.ViagemId.HasValue && request.ViagemExternoId.HasValue)
            {
                query = query.Where(v =>
                    v.Viagem.Id == request.ViagemId && v.Viagem.ViagemExternoId == request.ViagemExternoId);
            }
            else
            {
                if (request.ViagemId > 0)
                    query = query.Where(v => v.Viagem.Id == request.ViagemId);
            
                if (request.ViagemExternoId > 0)
                    query = query.Where(v => v.Viagem.ViagemExternoId == request.ViagemExternoId);
            }
            #endregion 
            
            #region 4. Filtro por CPF/CNPJ
            
            if (!string.IsNullOrWhiteSpace(request.CpfCnpj))
            {
                query = query.Where(v =>
                    v.Viagem.PortadorProprietario.CpfCnpj == request.CpfCnpj ||
                    v.Viagem.PortadorMotorista.CpfCnpj == request.CpfCnpj);
            }
            
            #endregion 
            
            #region  5. Filtro de datas
            if (request.DataInicial.HasValue)
                query = query.Where(v => v.DataCadastro >= request.DataInicial.Value.Date);

            if (request.DataFinal.HasValue)
                query = query.Where(v => v.DataCadastro < request.DataFinal.Value.Date.AddDays(1));
            #endregion 
            
            #region  6. GrupoStatus

            query = FiltrarPorGrupoStatus(query, request.GrupoStatus);
            
            #endregion
            
            #region  6.1 Ordenação
            query = request.Ordem.ToUpper() == "ASC"
                ? query.OrderBy(v => v.DataCadastro)
                : query.OrderByDescending(v => v.DataCadastro);
            #endregion 
            
            #region 7. Paginação

            var total = await query.CountAsync();
            var pagamentos = await query
                .Skip(request.Page * request.Limit)
                .Take(request.Limit)
                .ToListAsync();

            #endregion 
            
            #region 8. Mapeamento
            
            var mapped = 
                pagamentos.Select(p => Mapper.Map<PagamentoMobileResponse>(p)).ToList();
           
            #endregion 
            
            #region 9. Resposta final
            return new ConsultarPagamentoResponse
            {
                Page = request.Page,
                Limit = request.Limit,
                TotalPages = (int)Math.Ceiling((double)total / request.Limit),
                TotalItems = total,
                Result = mapped
            };
            #endregion 
        }
        
        public async Task<PagamentoMobileResponse> AlterarStatusAntecipacao(AtualizarStatusAntecipacaoRequest request)
        {
            #region 1. Validar filtros de entrada
            var validacao = request.validarFiltros();
            if (validacao is not null)
                throw new Exception(validacao);
            #endregion
        
            #region 2. Buscar o pagamento evento
            var pagamentoEvento = await _pagamentoEventoReadRepository.AsNoTracking()
                .Include(v => v.Viagem)
                .FirstOrDefaultAsync(v => v.Viagem.Id == request.ViagemId && v.Id == request.PagamentoId);
        
            if (pagamentoEvento == null)
                throw new Exception("Pagamento evento não encontrado para realizar a atualização");
            #endregion
        
            #region 3. Validar regras de negócio
            // Não atualiza status se o StatusAntecipacaoParcelaProprietario for igual a Aprovado ou Erro
            if (pagamentoEvento.StatusAntecipacaoParcelaProprietario is StatusAntecipacaoParcelaProprietario.Aprovado or StatusAntecipacaoParcelaProprietario.Erro)
            {
                throw new Exception("Não é possível alterar o status de uma parcela com status já aprovado ou com erro.");
            }
            #endregion
        
            #region 4. Converter enum do request para enum do domain

            var novoStatus = request.StatusAntecipacao.ParaStatusParcelaProprietario();
           
            #endregion
        
            #region 5. Tratar regras específicas para status Erro
            if (request.StatusAntecipacao == StatusAntecipacaoRequest.Erro)
            {
                // Verificar se já existe uma transação com FormaPagamento retencaoAntecipacao e fechada
                var transacaoRetencao = await _transacaoReadRepository.AsNoTracking()
                    .FirstOrDefaultAsync(t => t.IdPagamentoEvento == pagamentoEvento.Id &&
                                            (t.Descricao != null && t.Descricao.ToLower().Contains("retencao")) &&
                                            t.Status == StatusPagamento.Fechado);
        
                if (transacaoRetencao != null)
                {
                    // Cancelar a transação (fazer transação Origem/Destino inversa)
                    var transacaoCancelamento = new Domain.Models.Transacao.Commands.TransacaoSalvarCommand
                    {
                        IdPagamentoEvento = pagamentoEvento.Id,
                        //FormaPagamento = transacaoRetencao.FormaPagamento,
                        Status = StatusPagamento.Cancelado,
                        Valor = -transacaoRetencao.Valor,
                        Descricao = "Cancelamento de retenção de antecipação",
                        Origem = transacaoRetencao.Destino ?? 0,
                        Destino = transacaoRetencao.Origem
                    };
        
                    await Engine.CommandBus.SendCommandAsync(transacaoCancelamento);
        
                    // TODO REALZIAR nova transação (Empresa x Proprietário)
                    // var novaTransacao = new Domain.Models.Transacao.Commands.TransacaoSalvarCommand
                    // {
                    //     IdPagamentoEvento = pagamentoEvento.Id,
                    //     FormaPagamento = FormaPagamentoEvento.Deposito,
                    //     Status = StatusPagamento.Fechado,
                    //     Valor = transacaoRetencao.Valor,
                    //     Descricao = "Transferência para proprietário após erro na antecipação",
                    //     Origem = transacaoRetencao.Destino ?? 0,
                    //     Destino = transacaoRetencao.Origem
                    // };
        
                    // await Engine.CommandBus.SendCommandAsync(novaTransacao);
                }
            }
            #endregion
        
            #region 6. Atualizar o status do pagamento evento
            var pagamentoEventoParaAtualizar = await _pagamentoEventoReadRepository
                .FirstOrDefaultAsync(p => p.Id == pagamentoEvento.Id);
        
            if (pagamentoEventoParaAtualizar == null)
                return new PagamentoMobileResponse();
        
            pagamentoEventoParaAtualizar.StatusAntecipacaoParcelaProprietario = novoStatus;
            pagamentoEventoParaAtualizar.DataAlteracao = DateTime.Now;
        
            await _pagamentoEventoWriteRepository.UpdateAsync(pagamentoEventoParaAtualizar);
            await _pagamentoEventoWriteRepository.SaveChangesAsync();
            #endregion
        
            return new PagamentoMobileResponse();
        }
    
        
        private IQueryable<Domain.Models.PagamentoEvento.PagamentoEvento> FiltrarPorGrupoStatus(IQueryable<Domain.Models.PagamentoEvento.PagamentoEvento> query, string grupoStatus)
        {
            if (string.IsNullOrEmpty(grupoStatus)) return query;
            
            if (grupoStatus.Equals("Futuros", StringComparison.OrdinalIgnoreCase))
                return query.Where(v => v.Status == StatusPagamento.LancamentoProgramado);

            return grupoStatus.Equals("Recentes", StringComparison.OrdinalIgnoreCase) ? query.Where(v => v.Status != StatusPagamento.LancamentoProgramado) : query;
        }
    }
}