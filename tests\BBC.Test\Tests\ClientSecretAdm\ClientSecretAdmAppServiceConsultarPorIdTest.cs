using System;
using BBC.Test.Tests.ClientSecretAdm.Fixture;
using Moq;
using SistemaInfo.BBC.Application.Services.ClientSecretAdm;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm.Repository;
using Xunit;

namespace BBC.Test.Tests.ClientSecretAdm
{
    [Collection(nameof(ClientSecretAdmCollection))]
    public class ClientSecretAdmAppServiceConsultarPorIdTest
    {
        private readonly ClientSecretAdmFixture _fixture;
        private readonly ClientSecretAdmAppService _appService;
        private readonly Mock<IClientSecretAdmReadRepository> _readRepository;

        public ClientSecretAdmAppServiceConsultarPorIdTest(ClientSecretAdmFixture fixture)
        {
            _fixture = fixture;
            _appService = fixture.Mocker.CreateInstance<ClientSecretAdmAppService>();
            _readRepository = fixture.Mocker.GetMock<IClientSecretAdmReadRepository>();
        }
    
        [Fact(DisplayName = "ID inválido - Deve retornar falha")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.ConsultarPorId))]
        public async void ConsultarPorId_IdInvalido_RetornaFalha()
        {
            //Arrange
            var idInvalido = 0;
            var msgEsperada = "Não foi possível consultar o client secret adm. ID inválido.";
            var sucessoEsperado = false;

            //Action
            var resp = await _appService.ConsultarPorId(idInvalido);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.Equal(msgEsperada, resp.mensagem);
            Assert.Null(resp.data);
        }
    
        [Fact(DisplayName = "Client secret adm não encontrado - Deve retornar falha")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.ConsultarPorId))]
        public async void ConsultarPorId_NaoEncontrado_RetornaFalha()
        {
            //Arrange
            const int id = 99;
            var msgEsperada = "Client secret adm não encontrado.";
            var sucessoEsperado = false;

            _readRepository
                .Setup(c => c.GetByIdAsync(id))
                .ReturnsAsync((SistemaInfo.BBC.Domain.Models.ClientSecretAdm.ClientSecretAdm)null);

            //Action
            var resp = await _appService.ConsultarPorId(id);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.Equal(msgEsperada, resp.mensagem);
            Assert.Null(resp.data);
        }
    
        [Fact(DisplayName = "Consulta com sucesso - Senha deve ser omitida por segurança")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.ConsultarPorId))]
        public async void ConsultarPorId_ConsultaComSucesso_SenhaOmitida()
        {
            //Arrange
            var clientSecretAdm = _fixture.GerarClientSecretAdm();
            var senhaOriginal = clientSecretAdm.Senha;
            var msgEsperada = "Client secret adm consultado com sucesso.";
            var sucessoEsperado = true;

            _readRepository
                .Setup(c => c.GetByIdAsync(clientSecretAdm.Id))
                .ReturnsAsync(clientSecretAdm);

            //Action
            var resp = await _appService.ConsultarPorId(clientSecretAdm.Id);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.Equal(msgEsperada, resp.mensagem);
            Assert.NotNull(resp.data);
            
            // Verifica que a senha foi omitida por segurança
            var resultado = resp.data as dynamic;
            Assert.NotEqual(senhaOriginal, resultado?.Senha);
            Assert.True(string.IsNullOrEmpty(resultado?.Senha?.ToString()));
            
            // Verifica que outros dados foram mantidos
            Assert.Equal(clientSecretAdm.Id, resultado?.Id);
            Assert.Equal(clientSecretAdm.Login, resultado?.Login);
            Assert.Equal(clientSecretAdm.Descricao, resultado?.Descricao);
        }

        [Fact(DisplayName = "Exceção durante consulta - Deve retornar falha")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.ConsultarPorId))]
        public async void ConsultarPorId_ExcecaoNaConsulta_RetornaFalha()
        {
            //Arrange
            var id = 99;
            var msgExcecao = "Erro de conexão com banco de dados.";
            var msgEsperada = "Não foi possível consultar o client secret adm. " + msgExcecao;
            var sucessoEsperado = false;

            _readRepository
                .Setup(c => c.GetByIdAsync(id))
                .ThrowsAsync(new Exception(msgExcecao));

            //Action
            var resp = await _appService.ConsultarPorId(id);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.Equal(msgEsperada, resp.mensagem);
            Assert.Null(resp.data);
        }

        [Fact(DisplayName = "Consulta com GUID - Deve funcionar corretamente")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.ConsultarPorId))]
        public async void ConsultarPorId_ComGuid_FuncionaCorretamente()
        {
            //Arrange
            var clientSecretAdm = _fixture.GerarClientSecretAdm();
            var msgEsperada = "Client secret adm consultado com sucesso.";
            var sucessoEsperado = true;

            _readRepository
                .Setup(c => c.GetByIdAsync(clientSecretAdm.Id))
                .ReturnsAsync(clientSecretAdm);

            //Action
            var resp = await _appService.ConsultarPorId(clientSecretAdm.Id);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.Equal(msgEsperada, resp.mensagem);
            Assert.NotNull(resp.data);
            
            // Verifica que o GUID foi preservado corretamente
            var resultado = resp.data as dynamic;
            Assert.Equal(clientSecretAdm.Id, resultado?.Id);
        }

        [Fact(DisplayName = "Parâmetro correto na URL - Deve usar idAuthClientSecret")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.ConsultarPorId))]
        public async void ConsultarPorId_ParametroCorretoURL_UsaIdAuthClientSecret()
        {
            //Arrange
            var clientSecretAdm = _fixture.GerarClientSecretAdm();
            var msgEsperada = "Client secret adm consultado com sucesso.";
            var sucessoEsperado = true;

            _readRepository
                .Setup(c => c.GetByIdAsync(clientSecretAdm.Id))
                .ReturnsAsync(clientSecretAdm);

            //Action
            var resp = await _appService.ConsultarPorId(clientSecretAdm.Id);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.Equal(msgEsperada, resp.mensagem);
            Assert.NotNull(resp.data);
            
            // Verifica que o repositório foi chamado com o ID correto
            _readRepository.Verify(r => r.GetByIdAsync(clientSecretAdm.Id), Times.Once);
        }
    }
}
