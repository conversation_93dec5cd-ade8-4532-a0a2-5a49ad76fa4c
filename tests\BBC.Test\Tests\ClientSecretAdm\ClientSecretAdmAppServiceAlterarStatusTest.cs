using System;
using BBC.Test.Tests.ClientSecretAdm.Fixture;
using Moq;
using SistemaInfo.BBC.Application.Services.ClientSecretAdm;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using Xunit;

namespace BBC.Test.Tests.ClientSecretAdm
{
    [Collection(nameof(ClientSecretAdmCollection))]
    public class ClientSecretAdmAppServiceAlterarStatusTest
    {
        private readonly ClientSecretAdmFixture _fixture;
        private readonly ClientSecretAdmAppService _appService;
        private readonly Mock<IClientSecretAdmReadRepository> _readRepository;
        private readonly Mock<IClientSecretAdmWriteRepository> _writeRepository;

        public ClientSecretAdmAppServiceAlterarStatusTest(ClientSecretAdmFixture fixture)
        {
            _fixture = fixture;
            _appService = fixture.Mocker.CreateInstance<ClientSecretAdmAppService>();
            _readRepository = fixture.Mocker.GetMock<IClientSecretAdmReadRepository>();
            _writeRepository = fixture.Mocker.GetMock<IClientSecretAdmWriteRepository>();
        }
    
        [Fact(DisplayName = "Request nula - Deve retornar falha")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.AlterarStatus))]
        public async void AlterarStatus_RequestNula_RetornaFalha()
        {
            //Arrange
            var msgEsperada = "Não foi possível alterar o status. Requisição enviada com dados inválidos.";
            var sucessoEsperado = false;

            //Action
            var resp = await _appService.AlterarStatus(null);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.True(msgEsperada == resp.mensagem);
        }
    
        [Fact(DisplayName = "Client secret adm não encontrado - Deve retornar falha")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.AlterarStatus))]
        public async void AlterarStatus_Alterado_Sucesso()
        {
            //Arrange
            const int id = 99;
            var request =  new SistemaInfo.BBC.Application.Objects.Web.ClientSecretAdm.ClientSecretAdmAlterarStatusRequest { Id = id, Ativo = 1 };
            var msgEsperada = "Status alterado com sucesso.";
            var sucessoEsperado = true;

            _readRepository
                .Setup(c => c.GetByIdAsync(id))
                .ReturnsAsync((SistemaInfo.BBC.Domain.Models.ClientSecretAdm.ClientSecretAdm)null);

            //Action
            var resp = await _appService.AlterarStatus(request);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.True(msgEsperada == resp.mensagem);
        }
    
        [Fact(DisplayName = "Ativar client secret adm - Deve retornar sucesso")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.AlterarStatus))]
        public async void AlterarStatus_AtivarClientSecretAdm_RetornaSucesso()
        {
            //Arrange
            var clientSecretAdm = _fixture.GerarClientSecretAdm();
            clientSecretAdm.Ativo = 0; // Inativo
            var request = new SistemaInfo.BBC.Application.Objects.Web.ClientSecretAdm.ClientSecretAdmAlterarStatusRequest { Id = clientSecretAdm.Id, Ativo = 1 };
            var msgEsperada = "Status alterado com sucesso.";
            var sucessoEsperado = true;

            _readRepository
                .Setup(c => c.GetByIdAsync(request.Id))
                .ReturnsAsync(clientSecretAdm);

            _writeRepository
                .Setup(c => c.UpdateAsync(It.IsAny<SistemaInfo.BBC.Domain.Models.ClientSecretAdm.ClientSecretAdm>()))
                .Returns(System.Threading.Tasks.Task.CompletedTask);

            //Action
            var resp = await _appService.AlterarStatus(request);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.True(msgEsperada == resp.mensagem);
            
        }
    
        [Fact(DisplayName = "Desativar client secret adm - Deve retornar sucesso")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.AlterarStatus))]
        public async void AlterarStatus_DesativarClientSecretAdm_RetornaSucesso()
        {
            //Arrange
            var clientSecretAdm = _fixture.GerarClientSecretAdm();
            clientSecretAdm.Ativo = 1; // Ativo
            var request = new SistemaInfo.BBC.Application.Objects.Web.ClientSecretAdm.ClientSecretAdmAlterarStatusRequest { Id = clientSecretAdm.Id, Ativo = 0 };
            var msgEsperada = "Status alterado com sucesso.";
            var sucessoEsperado = true;

            _readRepository
                .Setup(c => c.GetByIdAsync(request.Id))
                .ReturnsAsync(clientSecretAdm);

            _writeRepository
                .Setup(c => c.UpdateAsync(It.IsAny<SistemaInfo.BBC.Domain.Models.ClientSecretAdm.ClientSecretAdm>()))
                .Returns(System.Threading.Tasks.Task.CompletedTask);

            //Action
            var resp = await _appService.AlterarStatus(request);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.True(msgEsperada == resp.mensagem);

        }

        [Fact(DisplayName = "Exceção durante alteração - Deve retornar falha")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.AlterarStatus))]
        public async void AlterarStatus_ExcecaoNaAlteracao_RetornaFalha()
        {
            //Arrange
            var id = 99;
            var request = new SistemaInfo.BBC.Application.Objects.Web.ClientSecretAdm.ClientSecretAdmAlterarStatusRequest { Id = id, Ativo = 1 };
            var msgExcecao = "Erro de conexão com banco de dados.";
            var msgEsperada = "Requisição enviada com dados inválidos.";
            var sucessoEsperado = false;

            _readRepository
                .Setup(c => c.GetByIdAsync(request.Id))
                .ThrowsAsync(new Exception(msgExcecao));

            //Action
            var resp = await _appService.AlterarStatus(null);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.True(msgEsperada == resp.mensagem);
        }

        [Fact(DisplayName = "Validação de segurança - Apenas admin pode alterar status")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.AlterarStatus))]
        public async void AlterarStatus_ValidacaoSeguranca_ApenasAdminPodeAlterar()
        {
            //Arrange
            var clientSecretAdm = _fixture.GerarClientSecretAdm();
            var request = new SistemaInfo.BBC.Application.Objects.Web.ClientSecretAdm.ClientSecretAdmAlterarStatusRequest { Id = clientSecretAdm.Id, Ativo = 1 };
            var usuario = _fixture.GerarUsuario();
            var msgEsperada = "Usuário sem permissão para alterar status de client secrets adm.";
            var sucessoEsperado = false;

            _readRepository
                .Setup(c => c.GetByIdAsync(request.Id))
                .ReturnsAsync(clientSecretAdm);

            _fixture.Mocker.GetMock<IAppEngine>()
                .Setup(c => c.User.Id)
                .Returns(usuario.Id);

            // Simula verificação de perfil
            _fixture.Mocker.GetMock<IAppEngine>()
                .Setup(c => c.User.EmpresaId == 0)
                .Returns(false);

            //Action
            var resp = await _appService.AlterarStatus(request);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.True(msgEsperada == resp.mensagem);
        }

        [Fact(DisplayName = "GUID como ID - Deve funcionar corretamente")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.AlterarStatus))]
        public async void AlterarStatus_GuidComoId_FuncionaCorretamente()
        {
            //Arrange
            var clientSecretAdm = _fixture.GerarClientSecretAdm();
            clientSecretAdm.Ativo = 0; // Inativo
            var request = new SistemaInfo.BBC.Application.Objects.Web.ClientSecretAdm.ClientSecretAdmAlterarStatusRequest{ Id = clientSecretAdm.Id, Ativo = 1 };
            var msgEsperada = "Client secret adm ativado com sucesso.";
            var sucessoEsperado = true;

            _readRepository
                .Setup(c => c.GetByIdAsync(request.Id))
                .ReturnsAsync(clientSecretAdm);

            _writeRepository
                .Setup(c => c.UpdateAsync(It.IsAny<SistemaInfo.BBC.Domain.Models.ClientSecretAdm.ClientSecretAdm>()))
                .Returns(System.Threading.Tasks.Task.CompletedTask);

            //Action
            var resp = await _appService.AlterarStatus(request);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.True(msgEsperada == resp.mensagem);
        }
    }
}
