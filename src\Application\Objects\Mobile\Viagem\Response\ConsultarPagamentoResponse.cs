﻿using System.Collections.Generic;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Response;

public class TransacaoMobileResponse
{
    public int Id { get; set; }
    public string DataCadastro { get; set; }
    public string DataAlteracao { get; set; }
    public string DataCancelamento { get; set; }
    public string DocumentoOrigem { get; set; }
    public string DocumentoDestino { get; set; }
    public string ContaOrigem { get; set; }
    public string ContaDestino { get; set; }
    public decimal Valor { get; set; }
    public string Status { get; set; }
    public string TransactionCodeCancelamento { get; set; }
    
    public string TransactionCodePagamento { get; set; }
    public string FormaPagamento { get; set; }
}

public class PagamentoMobileResponse
{
    public int Id { get; set; }
    public int? PagamentoExternoId { get; set; }
    public string RazaoSocialEmpresa { get; set; }
    public string RecebedorAutorizado { get; set; }
    public string DataCadastro { get; set; }
    public string DataAlteracao { get; set; }
    public string DataBaixa { get; set; }
    
    public string DataPrevisaoPagamento { get; set; }
    public string DataCancelamento { get; set; }
    public string FormaPagamento { get; set; }
    public string Status { get; set; }
    
    //public bool ParcelaAntecipada { get; set; }
    public string Tipo { get; set; }
    public decimal? ValorParcela { get; set; }
    public decimal? ValorTransferenciaMotorista { get; set; }
    public int ViagemId { get; set; }
    public int? ViagemExternoId { get; set; }
    public List<TransacaoMobileResponse> Transacoes { get; set; }
    
    public StatusAntecipacaoParcelaProprietario StatusAntecipacaoParcelaProprietario { get; set; }
}

public class ConsultarPagamentoResponse
{
    public int Page { get; set; }
    public int Limit { get; set; }
    public int TotalPages { get; set; }
    public int TotalItems { get; set; }
    public List<PagamentoMobileResponse> Result { get; set; }
}