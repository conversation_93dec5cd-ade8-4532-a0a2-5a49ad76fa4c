﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.Interface.Parametros;
using SistemaInfo.BBC.Application.Objects.Api.Parametros;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoAtualizacaoAutomaticaPrecoCombustivel;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoMonitoramentoCiot;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoQualificacaoTransacao;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoTelaoSaldo;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoTokenMobile;
using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoValePedagio;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Web.Attributes;
using SistemaInfo.BBC.Web.Controllers.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Web.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("Parametros")]
    public class ParametrosController : WebControllerBase<IParametrosAppService>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        public ParametrosController(IAppEngine engine, IParametrosAppService appService) : base(engine, appService)
        {
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("ConsultarGridParametro")]
        [Menu(new[] { EMenus.Parametros })]
        public JsonResult ConsultarGridParametro([FromBody] BaseGridRequest request)
        {
            var consultarGridParametro = AppService.ConsultarGridParametro(request.Take, request.Page, request.Order, request.Filters);
            return ResponseBase.ResponderSucesso(consultarGridParametro);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="tipoDoParametro"></param>
        /// <param name="tipoDoValor"></param>
        /// <returns></returns>
        [HttpGet("ConsultarPorId")]
        [Menu(new[] { EMenus.Parametros })]
        public JsonResult GetParametrosAsync(int id, Domain.Models.Parametros.Parametros.TipoDoParametro tipoDoParametro,
            Domain.Models.Parametros.Parametros.TipoDoValor tipoDoValor)
        {
            try
            {
                var consultar = AppService.GetParametrosAsync(id, tipoDoParametro, tipoDoValor).Result;
                return ResponseBase.ResponderSucesso(consultar);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Parâmetro não encontrado! Mensagem: " + e.Message);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="tipoDoParametro"></param>
        /// <param name="tipoDoValor"></param>
        /// <returns></returns>
        [HttpGet("ConsultarParametros")]
        [Menu(new[] { EMenus.Parametros })]
        public JsonResult GetParametrosListAsync(int id,
            Domain.Models.Parametros.Parametros.TipoDoParametro tipoDoParametro,
            Domain.Models.Parametros.Parametros.TipoDoValor tipoDoValor)
        {
            try
            {
                var lParametros = AppService.GetParametrosAsync(id, tipoDoParametro, tipoDoValor).Result;
                return ResponseBase.ResponderSucesso(lParametros);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Parâmetro não encontrado! Mensagem: " + e.Message);
            }
        }

        /// <summary>
        /// Sincroniza parametros gerais com o microserviço de pagamento de pedagio
        /// </summary>
        /// <returns></returns>
        [HttpGet("SincronizaParametrosComMicroServico")]
        [Menu(new[] { EMenus.Parametros })]
        public async Task<JsonResult> SincronizaParametrosComMicroServico() =>
            ResponseBase.Responder(await AppService.SincronizaParametrosComMicroServico());
        
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetMaxTentativasReenvioPedagio")]
        [Menu(new[] { EMenus.CentralPendencias })]
        public async Task<JsonResult> GetMaxTentativasReenvioPedagio() =>
            ResponseBase.BigJson(await AppService.GetMaxTentativasReenvioPedagio());
        
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetMaxTentativasReenvioFrete")]
        public async Task<JsonResult> GetMaxTentativasReenvioFrete() =>
            ResponseBase.BigJson(await AppService.GetMaxTentativasReenvioFrete());

        /// <summary>
        /// 
        /// </summary>
        /// <param name="parametros"></param>
        /// <returns></returns>
        [HttpPost("Cadastrar")]
        [Menu(new[] { EMenus.Parametros })]
        public async Task<JsonResult> Cadastrar([FromBody] ParametrosCadastrarRequest parametros)
        {
            try
            {
                var response = await AppService.Cadastrar(parametros);
                return !response.sucesso ? ResponseBase.ResponderErro(response.mensagem) : ResponseBase.ResponderSucesso(response);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="tipoDoParametro"></param>
        /// <param name="valor"></param>
        /// <param name="valorCriptografado"></param>
        /// <param name="tipoValor"></param>
        /// <returns></returns>
        [HttpPost("SaveParametro")]
        [Menu(new[] { EMenus.Parametros })]
        public JsonResult SaveParametro(int id,
            Domain.Models.Parametros.Parametros.TipoDoParametro tipoDoParametro, string valor, string valorCriptografado,
            Domain.Models.Parametros.Parametros.TipoDoValor tipoValor)
        {
            var lSaveParametro = AppService.SaveParametro(id, tipoDoParametro, valor, valorCriptografado, tipoValor);
            return ResponseBase.ResponderSucesso(lSaveParametro);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="parametroConfiguracaoSla"></param>
        /// <returns></returns>
        [HttpPost("SalvarConfiguracaoSla")]
        [Menu(new[] { EMenus.Parametros })]
        public async Task<JsonResult> SalvarConfiguracaoSla([FromBody] ParametroConfiguracaoSlaRequest parametroConfiguracaoSla)
        {
            try
            {
                var response = await AppService.SalvarConfiguracaoSla(parametroConfiguracaoSla);
                return !response.sucesso ? ResponseBase.ResponderErro(response.mensagem) : ResponseBase.ResponderSucesso(response);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("ConsultarParametroConfiguracaoSla")]
        [Menu(new[] { EMenus.Parametros })]
        public JsonResult ConsultarParametroConfiguracaoSla()
        {
            try
            {
                var consultar = AppService.ConsultarParametroConfiguracaoSla();
                return ResponseBase.ResponderSucesso(consultar);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Parâmetro não encontrado! Mensagem: " + e.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("ConfiguracaoAtualizacaoAutomaticaPrecoCombustivel")]
        [Menu(new[] { EMenus.Parametros })]
        public async Task<JsonResult> ConfiguracaoAtualizacaoAutomaticaPrecoCombustivel()
        {
            var retorno = await AppService.ConfiguracaoAtualizacaoAutomaticaPrecoCombustivel();
            return ResponseBase.Responder(retorno.sucesso, retorno.mensagem, retorno.data);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("SalvarConfiguracaoAtualizacaoAutomaticaPrecoCombustivel")]
        [Menu(new[] { EMenus.Parametros })]
        public async Task<JsonResult> SalvarConfiguracaoAtualizacaoAutomaticaPrecoCombustivel([FromBody] ConfiguracaoAtualizacaoAutomaticaPrecoCombustivelRequest request)
        {
            var retorno = await AppService.SalvarConfiguracaoAtualizacaoAutomaticaPrecoCombustivel(request);
            return ResponseBase.Responder(retorno.sucesso, retorno.mensagem, retorno.data);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("ConsultaParametrosConfiguracaoMonitoramentoCiot")]
        [Menu(new[] { EMenus.Parametros })]
        public async Task<JsonResult> ConsultaParametrosConfiguracaoMonitoramentoCiot()
        {
            var retorno = await AppService.ConsultaParametrosConfiguracaoMonitoramentoCiot();
            return ResponseBase.Responder(retorno.sucesso, retorno.mensagem, retorno.data);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("SalvarConfiguracaoMonitoramentoCiot")]
        [Menu(new[] { EMenus.Parametros })]
        public async Task<JsonResult> SalvarConfiguracaoMonitoramentoCiot([FromBody] ConfiguracaoMonitoramentoCiotRequest request)
        {
            var retorno = await AppService.SalvarConfiguracaoMonitoramentoCiot(request);
            return ResponseBase.Responder(retorno.sucesso, retorno.mensagem, retorno.data);
        }  
        
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("ConsultaParametrosConfiguracaoValePedagio")]
        [Menu(new[] { EMenus.Parametros })]
        public async Task<JsonResult> ConsultaParametrosConfiguracaoValePedagio()
        {
            var retorno = await AppService.ConsultaParametrosConfiguracaoValePedagio();
            return ResponseBase.Responder(retorno.sucesso, retorno.mensagem, retorno.data);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("SalvarConfiguracaoValePedagio")]
        [Menu(new[] { EMenus.Parametros })]
        public async Task<JsonResult> SalvarConfiguracaoValePedagio([FromBody] ConfiguracaoValePedagioRequest request)
        {
            var retorno = await AppService.SalvarConfiguracaoValePedagio(request);
            return ResponseBase.Responder(retorno.sucesso, retorno.mensagem, retorno.data);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("ConsultaParametrosConfiguracaoTelaoSaldo")]
        [Menu(new[] { EMenus.Parametros })]
        public async Task<JsonResult> ConsultaParametrosConfiguracaoTelaoSaldo()
        {
            var retorno = await AppService.ConsultaParametrosConfiguracaoTelaoSaldo();
            return ResponseBase.Responder(retorno.sucesso, retorno.mensagem, retorno.data);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("SalvarConfiguracaoTelaoSaldo")]
        [Menu(new[] { EMenus.Parametros })]
        public async Task<JsonResult> SalvarConfiguracaoValePedagio([FromBody] ConfiguracaoTelaoSaldoRequest request)
        {
            var retorno = await AppService.SalvarConfiguracaoTelaoSaldo(request);
            return ResponseBase.Responder(retorno.sucesso, retorno.mensagem, retorno.data);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("ConsultaParametrosConfiguracaoQualificacaoTransacao")]
        [Menu(new[] { EMenus.Parametros })]
        public async Task<JsonResult> ConsultaParametrosConfiguracaoQualificacaoTransacao()
        {
            var retorno = await AppService.ConsultaParametrosConfiguracaoQualificacaoTransacao();
            return ResponseBase.Responder(retorno.sucesso, retorno.mensagem, retorno.data);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("SalvarConfiguracaoQualificacaoTransacao")]
        [Menu(new[] { EMenus.Parametros })]
        public async Task<JsonResult> SalvarConfiguracaoQualificacaoTransacao([FromBody] ConfiguracaoQualificacaoTransacaoRequest request)
        {
            var retorno = await AppService.SalvarConfiguracaoQualificacaoTransacao(request);
            return ResponseBase.Responder(retorno.sucesso, retorno.mensagem, retorno.data);
        }
    }
}