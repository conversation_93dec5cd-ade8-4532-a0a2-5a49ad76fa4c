using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Application.Interface.ClientSecretAdm;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.ClientSecret;
using SistemaInfo.BBC.Application.Objects.Web.ClientSecretAdm;
using SistemaInfo.BBC.Application.Objects.Web.ClientSecretAdm.Request;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm.Commands;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.ClientSecretAdm
{
    public class ClientSecretAdmAppService : AppService<Domain.Models.ClientSecretAdm.ClientSecretAdm,
            IClientSecretAdmReadRepository, IClientSecretAdmWriteRepository>,
        IClientSecretAdmAppService
    {
        private readonly IUsuarioReadRepository _usuarioReadRepository;

        public ClientSecretAdmAppService(IAppEngine engine,
            IClientSecretAdmReadRepository readRepository,
            IClientSecretAdmWriteRepository writeRepository,
            IUsuarioReadRepository usuarioReadRepository)
            : base(engine, readRepository, writeRepository)
        {
            _usuarioReadRepository = usuarioReadRepository;
        }

        public async Task<RespPadrao> ConsultarPorId(int idClientSecretAdm)
        {
            try
            {
                var lClientSecretAdm = await Repository.Query
                    .Where(x => x.Id == idClientSecretAdm)
                    .ProjectTo<ConsultarClientSecretAdmResponse>(Engine.Mapper.ConfigurationProvider)
                    .FirstOrDefaultAsync();

                return new RespPadrao
                {
                    sucesso = true,
                    data = lClientSecretAdm
                };
            }
            catch (Exception e)
            {
                var lLog = LogManager.GetCurrentClassLogger();
                lLog.Error(e);
                return new RespPadrao(false, "Não foi possível consultar o client secret adm. " + e.Message);
            }
        }

        public async Task<RespPadrao> ConsultarGridClientSecretAdm(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            try
            {
                var lQuery = Repository.Query
                    .Include(x => x.Usuario)
                    .Include(x => x.UsuarioAlteracao)
                    .AsQueryable();

                // Aplicar filtros
                if (filters != null && filters.Any())
                {
                    foreach (var filter in filters)
                    {
                        switch (filter.Campo.ToLower())
                        {
                            case "login":
                                lQuery = lQuery.Where(x => x.Login.Contains(filter.Valor));
                                break;
                            case "descricao":
                                lQuery = lQuery.Where(x => x.Descricao.Contains(filter.Valor));
                                break;
                            case "ativo":
                                if (int.TryParse(filter.Valor, out int ativo))
                                    lQuery = lQuery.Where(x => x.Ativo == ativo);
                                break;
                        }
                    }
                }

                if (orderFilters != null && !string.IsNullOrEmpty(orderFilters.Campo))
                {
                    switch (orderFilters.Campo.ToLower())
                    {
                        case "login":
                            lQuery = orderFilters.Operador.ToString() == "ascending" 
                                ? lQuery.OrderBy(x => x.Login) 
                                : lQuery.OrderByDescending(x => x.Login);
                            break;
                        case "descricao":
                            lQuery = orderFilters.Operador.ToString() == "ascending" 
                                ? lQuery.OrderBy(x => x.Descricao) 
                                : lQuery.OrderByDescending(x => x.Descricao);
                            break;
                        case "datacadastro":
                            lQuery = orderFilters.Operador.ToString() == "ascending" 
                                ? lQuery.OrderBy(x => x.DataCadastro) 
                                : lQuery.OrderByDescending(x => x.DataCadastro);
                            break;
                        default:
                            lQuery = lQuery.OrderByDescending(x => x.DataCadastro);
                            break;
                    }
                }
                else
                {
                    lQuery = lQuery.OrderByDescending(x => x.DataCadastro);
                }

                var totalItems = await lQuery.CountAsync();

              
                var items = await lQuery
                    .ProjectTo<ConsultarClientSecretAdmGrid>(Engine.Mapper.ConfigurationProvider)
                    .Skip((page - 1) * take)
                    .Take(take)
                    .ToListAsync();

                return new RespPadrao
                {
                    sucesso = true,
                    data = new ConsultarGridClientSecretAdmResponse
                    {
                        TotalItems = totalItems,
                        Items = items
                    }
                };
            }
            catch (Exception e)
            {
                var lLog = LogManager.GetCurrentClassLogger();
                lLog.Error(e);
                return new RespPadrao(false, "Não foi possível consultar o grid de client secret adm. " + e.Message);
            }
        }

        public async Task<RespPadrao> SaveClientSecretAdm(ClientSecretAdmRequest lModel)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                if (lModel is null)
                    throw new InvalidOperationException("Requisição enviada com dados inválidos.");

                if (string.IsNullOrWhiteSpace(lModel.Login))
                    throw new InvalidOperationException("Login é obrigatório.");

                if (string.IsNullOrWhiteSpace(lModel.Descricao))
                    throw new InvalidOperationException("Descrição é obrigatória.");

                // Hash da senha se fornecida
                if (!string.IsNullOrWhiteSpace(lModel.Senha))
                    lModel.Senha = lModel.Senha.GetHashSha1();

                if (lModel.Id != 0)
                {
                    // Verificar se login já existe para outro registro
                    var existeOutro = await Repository.Query.AnyAsync(x => x.Login == lModel.Login && x.Id != lModel.Id && x.Ativo == 1);
                    if (existeOutro)
                        throw new InvalidOperationException("Já existe um client secret adm ativo com este login.");

                    var lClientSecretAdmUpdate = Mapper.Map<ClientSecretAdmEditarCommand>(lModel);
                    await Engine.CommandBus.SendCommandAsync(lClientSecretAdmUpdate);
                    return new RespPadrao()
                    {
                        sucesso = true,
                        mensagem = "Client secret adm editado com sucesso.",
                        data = lModel.ClientSecret
                    };
                }

                // Verificar se login já existe
                if (await Repository.Query.AnyAsync(x => x.Login == lModel.Login && x.Ativo == 1))
                    throw new InvalidOperationException("Já existe um client secret adm ativo com este login.");

                // Gerar client secret se não fornecido
                if (string.IsNullOrWhiteSpace(lModel.ClientSecret))
                {
                    lModel.ClientSecret = GenerateClientSecret(42);
                }

                var command = Mapper.Map<ClientSecretAdmAdicionarCommand>(lModel);
                command.DataCadastro = DateTime.Now;
                command.UsuarioCadastroId = Engine.User.Id;

                await Engine.CommandBus.SendCommandAsync(command);

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Client secret adm salvo com sucesso.",
                    data = lModel.ClientSecret
                };
            }
            catch (Exception e)
            {
                lLog.Error(e);
                return new RespPadrao(false, "Não foi possível salvar o client secret adm. " + e.Message);
            }
        }

        public async Task<RespPadrao> AlterarStatus(ClientSecretAdmAlterarStatusRequest lModel)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                if (lModel is null)
                    throw new InvalidOperationException("Requisição enviada com dados inválidos.");

                var command = Mapper.Map<ClientSecretAdmAlterarStatusCommand>(lModel);
                await Engine.CommandBus.SendCommandAsync(command);

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Status alterado com sucesso."
                };
            }
            catch (Exception e)
            {
                lLog.Error(e);
                return new RespPadrao(false, "Não foi possível alterar o status. " + e.Message);
            }
        }

        private string GenerateClientSecret(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }
    }
}
