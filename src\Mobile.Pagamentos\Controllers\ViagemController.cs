using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.Interface.Viagem;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Request;
using SistemaInfo.BBC.Mobile.Pagamentos.Controllers.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Mobile.Pagamentos.Controllers
{
    /// <summary>
    /// Classe da aplicação de pagamento
    /// </summary>
    [Route("Viagem")]
    public class ViagemController : ApiControllerBase
    {
        private readonly IViagemMobileAppService _viagemAppService;

        /// <summary>
        /// Injeção de dependecias e herança
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="viagemAppService"></param>
        public ViagemController(IAppEngine engine, IViagemMobileAppService viagemAppService) : base(engine)
        {
            _viagemAppService = viagemAppService;
        }


        /// <summary>
        /// Endpoint para retornar uma lista com todas as viagens do documento informado.
        /// </summary>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("ConsultarViagens")]
        public async Task<ObjectResult> ConsultarViagemMobile([FromBody] ConsultarViagensRequest request)
        {
            try
            {
                var lIntegrarPag = await _viagemAppService.ConsultarViagemMobile(request);

                if (lIntegrarPag == null || lIntegrarPag.TotalItems == 0)
                {
                    return NotFound("Nenhuma viagem encontrada para esse documento.");
                }
                return Ok(lIntegrarPag);
            }
            catch (Exception e)
            {
                return StatusCode(500, $"Não foi possível realizar a operação. {e.Message}");
            }
        }
        
        /// <summary>
        /// Endpoint para retornar uma lista com todos pagamentos do documento informado.
        /// </summary>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("ConsultarPagamentos")]
        public async Task<ObjectResult> ConsultarPagamentosMobile([FromBody] ConsultarPagamentosRequest request)
        {
            try
            {
                var lIntegrarPag = await _viagemAppService.ConsultarPagamentoMobile(request);
                if (lIntegrarPag == null || lIntegrarPag.TotalItems == 0)
                {
                    return NotFound("Nenhum pagamento encontrado para esse documento.");
                }
                return Ok(lIntegrarPag);
            }
            catch (Exception e)
            {
                return StatusCode(500, $"Não foi possível realizar a operação. {e.Message}");
            }
        }
        
        
        /// <summary>
        /// Endpoint para atualizar o status a respeito da antecipação de um pagamento.
        /// </summary>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPut("AtualizarStatusAntecipacao")]
        public async Task<ObjectResult> AtualizarStatusAntecipacao([FromBody] AtualizarStatusAntecipacaoRequest request)
        {
            try
            {
                var lIntegrarPag = await _viagemAppService.AlterarStatusAntecipacao(request);
                if (lIntegrarPag == null)
                {
                    return NotFound("Não foi possível alterar o status da antecipação");
                }
                return Ok(lIntegrarPag);
            }
            catch (Exception e)
            {
                return StatusCode(500, $"Não foi possível alterar o status da antecipação. {e.Message}");
            }
        }
    }
}