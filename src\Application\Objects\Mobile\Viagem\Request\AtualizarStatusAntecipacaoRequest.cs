﻿using System;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Request;

public class AtualizarStatusAntecipacaoRequest
{
    public int ViagemId { get; set; }
    public int PagamentoId { get; set; }
    public StatusAntecipacaoRequest StatusAntecipacao { get; set; }
    
    
    public string validarFiltros()
    {
        if (ViagemId <= 0)
        {
            return "Informe um ViagemId válido.";
        }

        if (PagamentoId <= 0)
        {
            return "Informe um PagamentoId válido.";
        }
        
        return null;
    }
    
}

public enum StatusAntecipacaoRequest
{
    Disponivel = 0,
    AguardandoProcessamento = 1,
    Aprovado = 2,
    Erro = 3
}

public static class StatusAntecipacaoExtensions
{
    public static StatusAntecipacaoParcelaProprietario ParaStatusParcelaProprietario(this StatusAntecipacaoRequest status)
    {
        return status switch
        {
            StatusAntecipacaoRequest.Disponivel => StatusAntecipacaoParcelaProprietario.Disponivel,
            StatusAntecipacaoRequest.AguardandoProcessamento => StatusAntecipacaoParcelaProprietario.AguardandoProcessamento,
            StatusAntecipacaoRequest.Aprovado => StatusAntecipacaoParcelaProprietario.Aprovado,
            StatusAntecipacaoRequest.Erro => StatusAntecipacaoParcelaProprietario.Erro,
            _ => throw new ArgumentOutOfRangeException(nameof(status), $"Status '{status}' não é suportado.")
        };
    }
}