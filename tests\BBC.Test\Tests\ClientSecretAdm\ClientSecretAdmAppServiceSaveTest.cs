using System;
using BBC.Test.Tests.ClientSecretAdm.Fixture;
using Moq;
using SistemaInfo.BBC.Application.Services.ClientSecretAdm;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm.Repository;
using Xunit;

namespace BBC.Test.Tests.ClientSecretAdm
{
    [Collection(nameof(ClientSecretAdmCollection))]
    public class ClientSecretAdmAppServiceSaveTest
    {
        private readonly ClientSecretAdmFixture _fixture;
        private readonly ClientSecretAdmAppService _appService;
        private readonly Mock<IClientSecretAdmReadRepository> _readRepository;

        public ClientSecretAdmAppServiceSaveTest(ClientSecretAdmFixture fixture)
        {
            _fixture = fixture;
            _appService = fixture.Mocker.CreateInstance<ClientSecretAdmAppService>();
            _readRepository = fixture.Mocker.GetMock<IClientSecretAdmReadRepository>();
        }
    
        [Fact(DisplayName = "Request nula - Deve retornar falha")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.SaveClientSecretAdm))]
        public async void SaveClientSecretAdm_RequestNula_RetornaFalha()
        {
            //Arrange
            var msgEsperada = "Não foi possível salvar o client secret adm. Requisição enviada com dados inválidos.";
            var sucessoEsperado = false;

            //Action
            var resp = await _appService.SaveClientSecretAdm(null);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.Equal(msgEsperada, resp.mensagem);
            Assert.Null(resp.data);
        }
    
        [Fact(DisplayName = "Login já existe - Deve retornar falha")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.SaveClientSecretAdm))]
        public async void SaveClientSecretAdm_LoginJaExiste_RetornaFalha()
        {
            //Arrange
            var msgEsperada = "Não foi possível salvar o client secret adm. Login já está em uso.";
            var sucessoEsperado = false;
            var req = _fixture.GerarClientSecretAdmRequest(novoRegistro: true);

            _readRepository
                .Setup(c => c.AnyAtivoAsync(req.Login))
                .ReturnsAsync(true);

            //Action
            var resp = await _appService.SaveClientSecretAdm(req);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.Equal(msgEsperada, resp.mensagem);
            Assert.Null(resp.data);
        }
    
        [Fact(DisplayName = "Campos readonly na edição - Login não pode ser alterado")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.SaveClientSecretAdm))]
        public async void SaveClientSecretAdm_EdicaoLoginReadonly_MantemLoginOriginal()
        {
            //Arrange
            var msgEsperada = "Client secret adm atualizado com sucesso.";
            var sucessoEsperado = true;
            var clientSecretAdmExistente = _fixture.GerarClientSecretAdm();
            var loginTentativaAlteracao = "novo_login_tentativa";

            var req = _fixture.GerarClientSecretAdmRequest();
            req.Id = clientSecretAdmExistente.Id;
            req.Login = loginTentativaAlteracao; // Tentativa de alterar login na edição

            _readRepository
                .Setup(c => c.GetByIdAsync(clientSecretAdmExistente.Id))
                .ReturnsAsync(clientSecretAdmExistente);

            _readRepository
                .Setup(c => c.AnyAtivoAsync(clientSecretAdmExistente.Login))
                .ReturnsAsync(false);

            //Action
            var resp = await _appService.SaveClientSecretAdm(req);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.Equal(msgEsperada, resp.mensagem);
            Assert.NotNull(resp.data);

            // Verifica que o repositório foi chamado para buscar o registro existente
            _readRepository.Verify(r => r.GetByIdAsync(clientSecretAdmExistente.Id), Times.Once);
        }

        [Fact(DisplayName = "Exceção durante salvamento - Deve retornar falha")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.SaveClientSecretAdm))]
        public async void SaveClientSecretAdm_ExcecaoNoSalvamento_RetornaFalha()
        {
            //Arrange
            var msgExcecao = "Erro de conexão com banco de dados.";
            var msgEsperada = "Não foi possível salvar o client secret adm. " + msgExcecao;
            var sucessoEsperado = false;
            var req = _fixture.GerarClientSecretAdmRequest();

            _readRepository
                .Setup(c => c.AnyAtivoAsync(req.Login))
                .ThrowsAsync(new Exception(msgExcecao));

            //Action
            var resp = await _appService.SaveClientSecretAdm(req);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.Equal(msgEsperada, resp.mensagem);
            Assert.Null(resp.data);
        }

        [Fact(DisplayName = "Criação com sucesso - Deve gerar client secret automaticamente")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.SaveClientSecretAdm))]
        public async void SaveClientSecretAdm_CriacaoComSucesso_GeraClientSecretAutomaticamente()
        {
            //Arrange
            var msgEsperada = "Client secret adm criado com sucesso.";
            var sucessoEsperado = true;
            var req = _fixture.GerarClientSecretAdmRequest(novoRegistro: true);
            req.ClientSecret = null; // Para testar geração automática

            _readRepository
                .Setup(c => c.AnyAtivoAsync(req.Login))
                .ReturnsAsync(false);

            //Action
            var resp = await _appService.SaveClientSecretAdm(req);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.Equal(msgEsperada, resp.mensagem);
            Assert.NotNull(resp.data);

            // Verifica que um client secret foi gerado automaticamente
            Assert.NotNull(resp.data.ToString());
        }

        [Fact(DisplayName = "Client Secret readonly na edição - Não pode ser alterado")]
        [Trait(nameof(ClientSecretAdmAppService), nameof(ClientSecretAdmAppService.SaveClientSecretAdm))]
        public async void SaveClientSecretAdm_EdicaoClientSecretReadonly_MantemClientSecretOriginal()
        {
            //Arrange
            var msgEsperada = "Client secret adm atualizado com sucesso.";
            var sucessoEsperado = true;
            var clientSecretAdmExistente = _fixture.GerarClientSecretAdm();
            var clientSecretTentativaAlteracao = Guid.NewGuid().ToString();

            var req = _fixture.GerarClientSecretAdmRequest();
            req.Id = clientSecretAdmExistente.Id;
            req.ClientSecret = clientSecretTentativaAlteracao; // Tentativa de alterar client secret na edição

            _readRepository
                .Setup(c => c.GetByIdAsync(clientSecretAdmExistente.Id))
                .ReturnsAsync(clientSecretAdmExistente);

            _readRepository
                .Setup(c => c.AnyAtivoAsync(req.Login))
                .ReturnsAsync(false);

            //Action
            var resp = await _appService.SaveClientSecretAdm(req);

            //Assert
            Assert.NotNull(resp);
            Assert.Equal(sucessoEsperado, resp.sucesso);
            Assert.Equal(msgEsperada, resp.mensagem);
            Assert.NotNull(resp.data);

            // Verifica que o client secret original foi mantido (não foi alterado)
            _readRepository.Verify(r => r.GetByIdAsync(clientSecretAdmExistente.Id), Times.Once);
        }
    }
}
